#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键词分析与提取系统
针对金色锥形生日派对帽的关键词分析
"""

import os
import re
from typing import List, Dict, Set, Tuple

class KeywordAnalyzer:
    """关键词分析与提取系统"""

    def __init__(self, product_info: Dict[str, str]):
        """
        初始化关键词分析器
        
        Args:
            product_info: 产品信息字典
        """
        self.product_info = product_info
        self.product_name = product_info.get('name', '')
        
        # 产品核心特性 - 精确定义
        self.product_features = {
            'color': ['gold', 'golden', '金色', '黑金', 'black and gold'],
            'shape': ['cone', 'cones', '锥形', '尖顶'],
            'material': ['paper', '纸', '纸制'],
            'pattern': ['star', 'polka dot', 'stripe', '星星', '波点', '条纹'],
            'occasion': ['party', 'birthday', 'celebration', '派对', '生日', '庆祝'],
            'item': ['hat', 'hats', 'cap', 'caps', '帽', '帽子'],
            'quantity': ['12', 'dozen', '12个', '一打'],
            'user': ['adult', 'kid', 'child', 'children', 'toddler', '成人', '儿童', '小孩']
        }
        
        # 产品规格 - 用于排除不符合的规格
        self.product_specs = {
            'correct_quantity': ['12', 'dozen', '12个', '一打'],
            'correct_color': ['gold', 'golden', 'black and gold', '金色', '黑金']
        }
        
        # 与产品不符的特性
        self.incompatible_specs = {
            'quantity': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', 
                        '13', '14', '15', '16', '18', '20', '24', '25', '30', '40', '50',
                        'single', 'pack of 6', 'pack of 8', 'pack of 10', 'pack of 20'],
            'color': [
                'pink', 'blue', 'red', 'white', 'purple', 'yellow', 'green', 
                'orange', 'silver', 'pastel', 'rainbow', 'multicolor',
                '粉色', '蓝色', '红色', '白色', '紫色', '黄色', '绿色', '橙色', '银色', '彩虹'
            ]
        }
        
        # 否定词列表 - 与产品不符的特性
        self.negative_terms = {
            # 不符合的颜色
            'pink', 'blue', 'red', 'white', 'purple', 'yellow', 'green', 'orange', 'silver', 'pastel',
            '粉色', '蓝色', '红色', '白色', '紫色', '黄色', '绿色', '橙色', '银色',
            
            # 不符合的材质
            'plastic', 'wooden', 'ceramic', 'glass', 'metal', 'fabric', 'foam',
            '塑料', '木质', '陶瓷', '玻璃', '金属', '布料', '泡沫',
            
            # 不符合的产品类型
            'mask', 'toy', 'game', 'book', 'food', 'drink', 'card', 'balloon', 'banner',
            'crown', 'tiara', 'headband', 'costume', 'outfit', 'dress',
            '面具', '玩具', '游戏', '书', '食物', '饮料', '卡片', '气球', '横幅',
            '皇冠', '头饰', '头带', '服装', '衣服',
            
            # 其他不相关词
            'diy', 'homemade', 'tutorial', 'how to', 'make', 'craft',
            '折纸', '教程', '制作', '如何', '手工', '制作'
        }
        
        # 品牌词列表 - 更全面的品牌词
        self.brand_terms = {
            # 主要零售商
            'amazon', 'walmart', 'target', 'etsy', 'party city', 'aliexpress',
            'michaels', 'hobby lobby', 'dollar tree', 'joann', 'ebay', 'shopee',
            'wayfair', 'temu', 'shein', 'wish', 'taobao', '1688', 'pinduoduo', 'jd',
            
            # 美国派对用品品牌
            'amscan', 'unique industries', 'beistle', 'meri meri', 'talking tables',
            'ginger ray', 'oriental trading', 'party central', 'party delights',
            'party packs', 'party pieces', 'shindigz', 'zurchers', 'party supply',
            
            # 英国派对用品品牌
            'hobbycraft', 'paperchase', 'flying tiger', 'wilko', 'the works',
            'poundland', 'the range', 'lakeland', 'partyrama', 'partydelights',
            
            # 亚洲派对用品品牌
            'daiso', 'miniso', 'mumuso', 'yoyoso', 'ilahui', 'artbox',
            
            # 知名派对用品品牌
            'hallmark', 'american greetings', 'papyrus', 'smiggle', 'claire\'s',
            'caspari', 'rifle paper co', 'oh happy day', 'momo party',
            
            # 国内派对用品品牌
            'partyyeah', 'ranvi', 'pbpbox', 'partyhood', 'funpa', 'boieo', 
            'bestoyard', 'toymytoy', 'amosfun', 'nuolux', 'toyvian', 'prettyia',
            
            # 迪士尼和授权品牌
            'disney', 'marvel', 'hello kitty', 'peppa pig', 'paw patrol',
            'mickey mouse', 'minnie mouse', 'frozen', 'princess', 'superman',
            'batman', 'spiderman', 'avengers'
        }
        
        # 初始化结果集
        self.core_keywords = set()
        self.long_tail_keywords = set()
        self.brand_keywords = set()
        self.negative_keywords = set()

    def _is_core_keyword(self, keyword: str) -> bool:
        """
        检查是否为核心关键词
        
        Args:
            keyword: 要检查的关键词
        
        Returns:
            是否是核心关键词
        """
        words = keyword.split()
        
        # 1-4个词的短语，直接相关的核心词
        if 1 <= len(words) <= 4:
            # 排除不符合产品规格的关键词
            # 检查数量词 - 如果关键词中包含数字，必须是12或者dozen
            has_incorrect_quantity = any(qty in keyword for qty in self.incompatible_specs['quantity'])
            
            # 检查颜色词 - 如果关键词中包含颜色，必须是金色或黑金
            has_incorrect_color = False
            for color in self.incompatible_specs['color']:
                # 确保匹配完整的词，避免误判
                if re.search(r'\b' + color + r'\b', keyword):
                    has_incorrect_color = True
                    break
            
            # 如果包含不符合的规格，不是核心关键词
            if has_incorrect_quantity or has_incorrect_color:
                return False
            
            # 必须包含帽子相关词
            has_hat = any(term in keyword for term in self.product_features['item'])
            if not has_hat:
                return False
                
            # 检查关键特性词
            has_color = any(term in keyword for term in self.product_features['color'])
            has_shape = any(term in keyword for term in self.product_features['shape'])
            has_occasion = any(term in keyword for term in self.product_features['occasion'])
            
            # 核心关键词标准：帽子词 + (颜色词 或 形状词 或 场合词) 中的至少一个
            # 构成了与产品直接相关的组合
            if has_hat and (has_color or has_shape or has_occasion):
                # 优先考虑具有多个特性的关键词
                # 如果同时包含颜色和形状，或颜色和场合，或形状和场合
                if (has_color and has_shape) or (has_color and has_occasion) or (has_shape and has_occasion):
                    return True
                
                # 单一特性的关键词只有在关键词短(1-2个词)时才考虑
                if len(words) <= 2:
                    return True
                
                # 如果是具有颜色特性的关键词，因为我们的产品主打金色，所以保留
                if has_color:
                    return True
                    
                # 如果是形状词特性的关键词，锥形是产品的核心特性，所以保留
                if has_shape:
                    return True
                
                # 其他情况，较长词组(3-4词)如果只有场合词，可能相关性不够高，不作为核心词
                return False
        
        return False

    def _is_long_tail_keyword(self, keyword: str) -> bool:
        """
        检查是否为长尾关键词
        
        Args:
            keyword: 要检查的关键词
        
        Returns:
            是否是长尾关键词
        """
        words = keyword.split()
        
        # 长尾关键词需要3个以上的词组
        if len(words) >= 3:
            # 排除不符合产品规格的关键词
            # 检查数量词 - 如果关键词中包含数字，必须是12或者dozen
            has_incorrect_quantity = any(qty in keyword for qty in self.incompatible_specs['quantity'])
            
            # 检查颜色词 - 如果关键词中包含颜色，必须是金色或黑金
            has_incorrect_color = False
            for color in self.incompatible_specs['color']:
                # 确保匹配完整的词，避免误判
                if re.search(r'\b' + color + r'\b', keyword):
                    has_incorrect_color = True
                    break
            
            # 如果包含不符合的规格，不是长尾关键词
            if has_incorrect_quantity or has_incorrect_color:
                return False
                
            # 必须包含帽子相关词
            has_hat = any(term in keyword for term in self.product_features['item'])
            if not has_hat:
                return False
            
            # 必须包含产品核心特性之一
            has_color = any(term in keyword for term in self.product_features['color'])
            has_shape = any(term in keyword for term in self.product_features['shape'])
            has_occasion = any(term in keyword for term in self.product_features['occasion'])
            has_pattern = any(term in keyword for term in self.product_features['pattern'])
            has_material = any(term in keyword for term in self.product_features['material'])
            has_user = any(term in keyword for term in self.product_features['user'])
            
            # 高质量长尾关键词需要结合多个产品特性
            # 1. 同时包含形状和颜色 - 这是产品的核心特性组合
            if has_shape and has_color:
                return True
                
            # 2. 同时包含颜色和场合 - 这是产品的主要使用场景
            if has_color and has_occasion:
                return True
                
            # 3. 同时包含形状和场合 - 核心特性和使用场景的组合
            if has_shape and has_occasion:
                return True
                
            # 4. 黑金配色相关的长尾词 - 特殊重点
            if "black and gold" in keyword and (has_hat or has_occasion or has_shape):
                return True
                
            # 5. 帽子+颜色/形状+用户群体 - 面向特定用户群体的产品
            if has_hat and (has_color or has_shape) and has_user:
                return True
                
            # 6. 帽子+颜色/形状+材质 - 强调产品材质特性
            if has_hat and (has_color or has_shape) and has_material:
                return True
                
            # 7. 场合+用户群体+颜色/形状 - 特定场合下针对特定用户的产品
            if has_occasion and has_user and (has_color or has_shape):
                return True
            
            # 8. 包含图案关键词 - 产品图案是一个卖点
            if has_pattern and (has_color or has_shape or has_occasion):
                return True
                
            # 9. 必须是12个装的关键词 - 强调数量
            has_correct_quantity = any(term in keyword for term in self.product_specs['correct_quantity'])
            if has_correct_quantity and (has_color or has_shape or has_occasion):
                return True
                
            # 其他情况不满足高质量长尾关键词标准
            return False
            
        return False

    def _is_brand_keyword(self, keyword: str) -> bool:
        """
        检查是否包含品牌名称
        
        Args:
            keyword: 要检查的关键词
        
        Returns:
            是否包含品牌名称
        """
        # 检查是否包含帽子相关词或派对/生日相关词
        has_hat = any(term in keyword for term in self.product_features['item'])
        has_occasion = any(term in keyword for term in self.product_features['occasion'])
        has_shape = any(term in keyword for term in self.product_features['shape'])
        
        # 检查是否包含任何品牌词
        has_brand = False
        for brand in self.brand_terms:
            # 确保匹配完整的词，避免误判
            if re.search(r'\b' + brand + r'\b', keyword):
                has_brand = True
                break
        
        # 放宽条件：包含品牌词，且包含帽子词或场合词或形状词
        return has_brand and (has_hat or has_occasion or has_shape)

    def analyze_keywords(self, keywords: List[str]) -> None:
        """
        分析关键词
        
        Args:
            keywords: 关键词列表
        """
        # 使用集合去除重复关键词，并清理关键词
        unique_keywords = set()
        for keyword in keywords:
            keyword = keyword.strip().lower()
            if keyword:  # 跳过空关键词
                unique_keywords.add(keyword)
        
        # 清空之前的分析结果
        self.core_keywords = set()
        self.long_tail_keywords = set()
        self.brand_keywords = set()
        self.negative_keywords = set()
        
        # 分析每个关键词
        for keyword in unique_keywords:
            words = keyword.split()
            
            # 检查是否是否定词
            if len(words) == 1 and keyword in self.negative_terms:
                self.negative_keywords.add(keyword)
                continue
            
            # 检查是否包含产品不符的特性（错误的颜色、材质等）
            is_negative = False
            for neg_term in self.negative_terms:
                if re.search(r'\b' + neg_term + r'\b', keyword) and not self._is_exception(keyword, neg_term):
                    # 如果包含否定词，且不是例外情况，标记为否定
                    is_negative = True
                    if len(neg_term.split()) == 1:  # 如果是单个词的否定词
                        self.negative_keywords.add(neg_term)
            
            if is_negative:
                continue  # 跳过不符合产品特性的关键词
            
            # 检查是否包含品牌名称
            if self._is_brand_keyword(keyword):
                self.brand_keywords.add(keyword)
                continue
            
            # 判断关键词是否与产品相关
            if not self._is_related_to_party_hat(keyword):
                continue
                
            # 判断核心关键词 - 扩展判断逻辑
            if self._is_core_keyword(keyword):
                self.core_keywords.add(keyword)
                continue
            
            # 判断长尾关键词
            if self._is_long_tail_keyword(keyword):
                self.long_tail_keywords.add(keyword)
                continue

    def _is_exception(self, keyword: str, negative_term: str) -> bool:
        """
        检查是否为例外情况（某些包含否定词但实际与产品相关的情况）
        
        Args:
            keyword: 要检查的关键词
            negative_term: 否定词
        
        Returns:
            是否是例外情况
        """
        # 例如，"not red"或"instead of plastic"这样的短语
        exception_phrases = ['not ' + negative_term, 'no ' + negative_term, 'instead of ' + negative_term]
        return any(phrase in keyword for phrase in exception_phrases)

    def _is_related_to_party_hat(self, keyword: str) -> bool:
        """
        检查关键词是否与派对帽相关
        
        Args:
            keyword: 要检查的关键词
        
        Returns:
            是否与派对帽相关
        """
        # 必须含有帽子相关词
        has_hat = any(term in keyword for term in self.product_features['item'])
        
        # 必须含有场合相关词或形状相关词
        has_occasion = any(term in keyword for term in self.product_features['occasion'])
        has_shape = any(term in keyword for term in self.product_features['shape'])
        
        return has_hat and (has_occasion or has_shape)

    def sort_results(self) -> None:
        """对结果进行排序"""
        # 核心关键词按相关性排序（这里按照产品特征的匹配度排序）
        def core_relevance(keyword):
            score = 0
            
            # 颜色特性
            if 'gold' in keyword or '金色' in keyword:
                score += 5
            if 'black and gold' in keyword or '黑金' in keyword:
                score += 6
                
            # 形状特性
            if 'cone' in keyword or '锥形' in keyword:
                score += 4
                
            # 场合特性
            if 'party' in keyword or '派对' in keyword:
                score += 3
            if 'birthday' in keyword or '生日' in keyword:
                score += 3
                
            # 物品特性
            if 'hat' in keyword or '帽' in keyword:
                score += 2
                
            # 图案特性
            if 'star' in keyword or '星星' in keyword:
                score += 1
            if 'polka dot' in keyword or '波点' in keyword:
                score += 1
            if 'stripe' in keyword or '条纹' in keyword:
                score += 1
                
            # 单词数量 - 更精确的短语得分更高
            score += min(len(keyword.split()), 4)
            
            return -score  # 负分，以便高分在前
            
        self.core_keywords = sorted(self.core_keywords, key=core_relevance)
        
        # 长尾关键词按相关性排序（同样按特征匹配度）
        self.long_tail_keywords = sorted(self.long_tail_keywords, key=core_relevance)
        
        # 品牌关键词按字母顺序排序
        self.brand_keywords = sorted(self.brand_keywords)
        
        # 否定关键词按字母顺序排序
        self.negative_keywords = sorted(self.negative_keywords)

    def get_results(self) -> Dict[str, List[str]]:
        """获取分析结果"""
        self.sort_results()
        
        return {
            'core_keywords': list(self.core_keywords),
            'long_tail_keywords': list(self.long_tail_keywords),
            'brand_keywords': list(self.brand_keywords),
            'negative_keywords': list(self.negative_keywords)
        }

    def print_results(self, limit: int = None) -> None:
        """
        打印分析结果
        
        Args:
            limit: 限制每类显示的关键词数量
        """
        results = self.get_results()
        
        print("\n核心关键词:")
        print("```")
        for keyword in results['core_keywords'][:limit]:
            print(keyword)
        print("```")
        
        print("\n长尾关键词:")
        print("```")
        for keyword in results['long_tail_keywords'][:limit]:
            print(keyword)
        print("```")
        
        print("\n品牌竞品关键词:")
        print("```")
        for keyword in results['brand_keywords'][:limit]:
            print(keyword)
        print("```")
        
        print("\n否定关键词:")
        print("```")
        for keyword in results['negative_keywords'][:limit]:
            print(keyword)
        print("```")

def parse_product_info(raw_info: str) -> Dict[str, str]:
    """解析产品信息文本"""
    product_info = {}
    
    # 尝试提取产品名称
    name_match = re.search(r"产品名称：(.+?)(?:\n|$)", raw_info)
    if name_match:
        product_info['name'] = name_match.group(1).strip()
    
    # 尝试提取颜色
    color_match = re.search(r"颜色:(.+?)(?:\n|$)", raw_info)
    if color_match:
        product_info['color'] = color_match.group(1).strip()
    
    # 尝试提取数量
    quantity_match = re.search(r"数量：(.+?)(?:\n|$)", raw_info)
    if quantity_match:
        product_info['quantity'] = quantity_match.group(1).strip()
    
    # 尝试提取材质
    material_match = re.search(r"材质：(.+?)(?:\n|$)", raw_info)
    if material_match:
        product_info['material'] = material_match.group(1).strip()
    
    # 尝试提取尺寸
    size_match = re.search(r"尺寸：(.+?)(?:\n|$)", raw_info)
    if size_match:
        product_info['size'] = size_match.group(1).strip()
    
    # 尝试提取描述（剩余内容）
    descriptions = []
    lines = raw_info.split('\n')
    for i, line in enumerate(lines):
        if i > 4 and line.strip() and not line.startswith("产品名称") and not line.startswith("颜色") and not line.startswith("数量") and not line.startswith("材质") and not line.startswith("尺寸"):
            descriptions.append(line.strip())
    
    if descriptions:
        product_info['description'] = " ".join(descriptions)
    
    return product_info

def main():
    # 产品信息
    product_info_text = """
产品名称：金色锥形生日派对帽
颜色:金色
数量：12个（三种图案，各4个）,配有12条绳子
材质：纸制
尺寸：帽子直径14cm，高20cm；绳长30cm
金色波点图案，金色条纹图案，金色星星搭配黑色波点图案,适合各类派对场合，
通用尺寸设计让大人小孩都能轻松驾驭,配套的系带确保佩戴稳固舒适
    """
    
    # 解析产品信息
    product_info = parse_product_info(product_info_text)
    
    # 从文件读取关键词
    keywords_file = r"c:\Users\<USER>\Desktop\cursoryh\2025广告优化\boieo店铺\3色生日帽\3色生日帽AI筛选"
    try:
        with open(keywords_file, 'r', encoding='utf-8') as f:
            file_keywords = [line.strip() for line in f if line.strip()]
        print(f"成功从文件中读取 {len(file_keywords)} 个关键词")
    except Exception as e:
        print(f"读取文件失败: {e}")
        file_keywords = []
    
    # 创建关键词分析器
    analyzer = KeywordAnalyzer(product_info)
    
    # 分析关键词
    analyzer.analyze_keywords(file_keywords)
    
    # 获取分析结果
    results = analyzer.get_results()
    
    # 输出目录
    output_dir = r"c:\Users\<USER>\Desktop\cursoryh\2025广告优化\boieo店铺\3色生日帽\关键词分析结果"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存核心关键词
    core_file = os.path.join(output_dir, "核心关键词.txt")
    with open(core_file, "w", encoding="utf-8") as f:
        for keyword in results["core_keywords"]:
            f.write(f"{keyword}\n")
    print(f"已保存核心关键词到: {core_file}")
    
    # 保存长尾关键词
    long_tail_file = os.path.join(output_dir, "长尾关键词.txt")
    with open(long_tail_file, "w", encoding="utf-8") as f:
        for keyword in results["long_tail_keywords"]:
            f.write(f"{keyword}\n")
    print(f"已保存长尾关键词到: {long_tail_file}")
    
    # 保存品牌竞品关键词
    brand_file = os.path.join(output_dir, "品牌竞品关键词.txt")
    with open(brand_file, "w", encoding="utf-8") as f:
        for keyword in results["brand_keywords"]:
            f.write(f"{keyword}\n")
    print(f"已保存品牌竞品关键词到: {brand_file}")
    
    # 保存否定关键词
    negative_file = os.path.join(output_dir, "否定关键词.txt")
    with open(negative_file, "w", encoding="utf-8") as f:
        for keyword in results["negative_keywords"]:
            f.write(f"{keyword}\n")
    print(f"已保存否定关键词到: {negative_file}")
    
    print(f"\n分析结果已保存到 {output_dir} 目录\n")
    
    # 打印分析结果
    analyzer.print_results(limit=100)  # 限制显示前100个关键词
    
    # 打印验证和建议
    print("\n## 验证要点")
    print("\n### 1. 完整性检查")
    print("- 涵盖了产品所有核心功能和特点：锥形帽、生日帽、派对帽、金色、纸制、成人/儿童适用")
    print("- 包含了所有相关品牌词")
    print("- 否定词列表全面涵盖了与产品不相关的颜色、材质和产品类型")
    
    print("\n### 2. 准确性验证")
    print("- 核心关键词聚焦于产品的主要特征：金色、锥形、生日帽")
    print("- 长尾关键词准确描述了具体场景和用户群体")
    print("- 品牌关键词仅包含与派对用品相关的品牌")
    print("- 否定关键词避免了无关颜色（如粉色、紫色）和材质（如木质、塑料）")
    
    print("\n### 3. 使用建议")
    print("\n#### 关键词优先级")
    print("1. 最高优先级：金色特定关键词，如\"gold party hats\"、\"gold cone hat\"")
    print("2. 中等优先级：成人/儿童特定关键词，如\"party hats for adults\"、\"kids birthday party hats\"")
    print("3. 低优先级：一般派对关键词，如\"party hats\"、\"birthday hats\"")
    
    print("\n#### 注意事项说明")
    print("- 避免使用过于宽泛的关键词如单独的\"hat\"或\"party\"")
    print("- 监控\"gold\"相关关键词的竞争度，视情况调整投放")
    print("- 优先关注\"黑金\"配色关键词，因为这是产品的特色图案之一")
    
    print("\n#### 定期优化方向")
    print("- 监控转化率最高的关键词，增加投放")
    print("- 根据节日和季节性活动（如新年、圣诞节、毕业季）调整关键词优先级")
    print("- 定期扩展否定关键词列表，排除低效流量")

if __name__ == "__main__":
    main()
