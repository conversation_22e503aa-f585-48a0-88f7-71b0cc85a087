# 亚马逊广告否定关键词精准提取系统

## 系统目标

- 从大量搜索词中精准提取不相关词汇用于否定
- 避免误杀潜在相关词汇，提高广告投放ROI
- 减少无效点击和费用浪费

## 精准提取提示词模板

任务：基于产品信息和搜索词数据，精准识别应当否定的无关搜索词

满足以下任一条件的词语应被提取为否定关键词：

1. 产品匹配度得分为0-1分
2. 用户匹配度得分为0分
3. 直接指向竞争产品的专属词汇
4. 明确是不相关产品/用途的词汇
5. 与产品类别完全无关的词（如吸管产品出现"furniture/家具"）
6. 描述与产品完全相反属性的词（如可重复使用产品出现"disposable/一次性"）
7. 明显属于其他产品类别的专属词
8. 与产品的目标用户群体、使用场景完全无关的词
9. 使用该词会导致产品被错误归类或被错误用户群体发现
10. 在亚马逊搜索时，用户输入该词绝不可能是在寻找此类产品
11. 与产品颜色/形状不同的词
12. 与产品形态不同的词，（如产品是土豆玩偶，出现pig）
13. 早期研究阶段，购买意向较低
14. 纯信息搜索，几乎无购买意向
15. 完全无购买意向（如教育性内容搜索）
16. 可能但不太可能是目标用户

## 输出要求

输出两个部分：

1. **纯否定词列表**：
   - 只包含否定词，无任何解释
   - 单个单词格式（halloween，costume，covid，shield，dentist，plant）如，每行一个
   - 方便直接复制使用

2. **详细分析版**：
   - 为每个否定词提供多维度评分和详细理由（如halloween -完全不同产品类别，针对装饰而非护肤）
